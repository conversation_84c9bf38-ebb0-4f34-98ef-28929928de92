#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试元认知智能体的临时脚本
"""

import asyncio
import logging
import json
from meta_cognitive_agent import get_meta_cognitive_agent

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def debug_agent():
    test_text = 'President <PERSON> points this way today , even the most frivolous of lawsuits cost money , premiums go up and either way , the patient pays .'
    
    print(f'测试文本: {test_text}')
    print('='*60)
    
    try:
        agent = get_meta_cognitive_agent()
        result = await agent.extract_entities(test_text)
        
        print(f'最终结果: {result}')
        print(f'结果类型: {type(result)}')
        print(f'结果是否为空: {not result}')
        
        if isinstance(result, dict):
            print('字典内容分析:')
            for key, value in result.items():
                print(f'  {key}: {value} (长度: {len(value) if isinstance(value, list) else "非列表"})')
            
            total_entities = sum(len(v) for v in result.values() if isinstance(v, list))
            print(f'总实体数: {total_entities}')
            
    except Exception as e:
        print(f'处理失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_agent())
