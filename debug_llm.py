#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试LLM响应的临时脚本
"""

import asyncio
import logging
import json
from model_interface import model_service
from schemas import RetrieveNERExamplesTool, ExtractionTool

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

async def debug_llm_response():
    test_text = 'President <PERSON> points this way today , even the most frivolous of lawsuits cost money , premiums go up and either way , the patient pays .'
    
    # 构建简化的直接NER prompt
    prompt = f"""You are an expert Named Entity Recognition system.

**IMPORTANT: Use ONLY these entity types from ACE 2005 dataset:**
- person: People, individuals, groups (e.g., "<PERSON>", "Mary Johnson", "the team")
- organization: Companies, institutions, agencies (e.g., "Apple Inc.", "Microsoft", "FBI")
- location: Places, addresses, geographic locations (e.g., "New York", "California", "Main Street")
- facility: Buildings, structures, installations (e.g., "White House", "airport", "hospital")
- weapon: Weapons, armaments (e.g., "rifle", "missile", "bomb")
- vehicle: Transportation vehicles (e.g., "car", "airplane", "ship")
- geo-political: Geographic and political entities (e.g., "United States", "European Union", "NATO")

Extract named entities from the following text using these entity types: person, organization, location, facility, weapon, vehicle, geo-political

Text to analyze: "{test_text}"

Extract all named entities and return them using the extract_entities tool."""

    print('发送的Prompt:')
    print('='*60)
    print(prompt)
    print('='*60)
    
    tools = [ExtractionTool]
    messages = [{'role': 'user', 'content': prompt}]
    
    try:
        response = await model_service.generate_with_tools_async(
            messages=messages,
            tools=tools
        )
        
        print('LLM响应:')
        print(f'response: {response}')
        print(f'type: {type(response)}')
        
        if hasattr(response, 'tool_calls'):
            print(f'tool_calls: {response.tool_calls}')
            if response.tool_calls:
                for i, tool_call in enumerate(response.tool_calls):
                    print(f'tool_call {i}: {tool_call}')
                    if hasattr(tool_call, 'function'):
                        print(f'  function: {tool_call.function}')
                        if hasattr(tool_call.function, 'name'):
                            print(f'  function.name: {tool_call.function.name}')
                        if hasattr(tool_call.function, 'arguments'):
                            print(f'  function.arguments: {tool_call.function.arguments}')
                            try:
                                args = json.loads(tool_call.function.arguments)
                                print(f'  parsed args: {json.dumps(args, indent=2, ensure_ascii=False)}')
                            except:
                                print('  failed to parse arguments')
        
        if hasattr(response, 'content'):
            print(f'content: {response.content}')
            
    except Exception as e:
        print(f'API调用失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_llm_response())
