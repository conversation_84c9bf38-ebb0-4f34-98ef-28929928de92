#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试pipeline处理逻辑的临时脚本
"""

import asyncio
import logging
import json
from pipeline import run_meta_cognitive_ner_pipeline

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def debug_pipeline():
    test_text = 'President <PERSON> points this way today , even the most frivolous of lawsuits cost money , premiums go up and either way , the patient pays .'
    
    print(f'测试文本: {test_text}')
    print('='*60)
    
    try:
        result = await run_meta_cognitive_ner_pipeline(test_text)
        print(f'原始结果: {result}')
        print(f'结果类型: {type(result)}')
        print(f'结果是否为空: {not result}')
        
        if isinstance(result, dict):
            print('字典内容分析:')
            for key, value in result.items():
                print(f'  {key}: {value} (长度: {len(value) if isinstance(value, list) else "非列表"})')
            
            total_entities = sum(len(v) for v in result.values() if isinstance(v, list))
            print(f'总实体数: {total_entities}')
            
            # 过滤空数组
            filtered_result = {k: v for k, v in result.items() if isinstance(v, list) and v}
            print(f'过滤后结果: {filtered_result}')
            
    except Exception as e:
        print(f'处理失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_pipeline())
