"""
示例检索器 - 简单高效的NER示例检索系统
遵循KISS原则：保持简单，避免过度设计
"""

import asyncio
import logging
import numpy as np
from typing import List, Dict, Any, Optional
import json
import os
import time
import pickle
import hashlib
import threading

from schemas import RetrievalCriteria
from model_interface import model_service
from config import CONFIG, get_current_dataset_path

logger = logging.getLogger(__name__)


class SimpleVectorStore:
    """内存向量存储 - 支持多维度和共享矩阵缓存"""

    def __init__(self):
        self.examples = []
        self.embeddings = []
        self.metadata = []
        # 🚀 多维度共享矩阵缓存
        self._matrix_cache = {}
        self._cache_lock = threading.Lock()
        self._cache_valid = False

    def add_examples(self, examples: List[Dict[str, Any]], embeddings: List[List[float]]):
        """添加示例和对应的嵌入向量"""
        self.examples.extend(examples)
        self.embeddings.extend(embeddings)

        # 🔍 清除缓存，因为数据已更新
        with self._cache_lock:
            self._matrix_cache.clear()
            self._cache_valid = False

        # 提取基本元数据，并将embedding添加到每个example中
        for i, example in enumerate(examples):
            text = example.get('text', '')

            # 提取结构标签
            has_passive_voice = 'was ' in text or 'were ' in text or 'been ' in text
            has_numbers = any(char.isdigit() for char in text)
            has_abbreviations = any(word.isupper() and len(word) > 1 for word in text.split())

            metadata = {
                'entity_types': list(example.get('label', {}).keys()),
                'entity_count': sum(len(entities) for entities in example.get('label', {}).values()),
                'text_length': len(text),
                'has_passive_voice': has_passive_voice,
                'has_numbers': has_numbers,
                'has_abbreviations': has_abbreviations
            }
            self.metadata.append(metadata)

            # 将embedding添加到example中，方便后续检索
            if i < len(embeddings):
                example['embedding'] = embeddings[i]

    def get_embeddings_matrix(self, dimension_key: str = "default") -> np.ndarray:
        """🚀 获取共享的embeddings矩阵，支持多维度缓存"""
        if not self.embeddings:
            return np.array([])

        # 检查缓存
        if dimension_key in self._matrix_cache and self._cache_valid:
            return self._matrix_cache[dimension_key]

        # 创建矩阵并缓存
        with self._cache_lock:
            if dimension_key not in self._matrix_cache or not self._cache_valid:
                embeddings_matrix = np.array(self.embeddings)
                self._matrix_cache[dimension_key] = embeddings_matrix
                self._cache_valid = True
                logger.debug(f"✅ 缓存embeddings矩阵: {embeddings_matrix.shape}, 维度: {dimension_key}")

        return self._matrix_cache[dimension_key]

    def similarity_search(self, query_embedding: List[float], top_k: int = 10, dimension_key: str = "default") -> List[Dict[str, Any]]:
        """🚀 使用共享矩阵进行高效向量搜索"""
        if not self.embeddings:
            return []

        query_vec = np.array(query_embedding)
        # 🔍 使用共享的embeddings矩阵，避免重复创建
        embeddings_matrix = self.get_embeddings_matrix(dimension_key)

        # 计算余弦相似度
        similarities = np.dot(embeddings_matrix, query_vec) / (
            np.linalg.norm(embeddings_matrix, axis=1) * np.linalg.norm(query_vec)
        )

        # 获取top_k结果
        top_indices = np.argsort(similarities)[::-1][:top_k]

        results = []
        for idx in top_indices:
            results.append({
                **self.examples[idx],
                'similarity_score': similarities[idx]
            })

        return results

    def filter_by_metadata(self, filter_conditions: Dict[str, Any]) -> List[Dict[str, Any]]:
        """🔍 根据元数据条件过滤示例"""
        try:
            if not filter_conditions:
                return self.examples.copy()

            filtered_examples = []

            for i, example in enumerate(self.examples):
                metadata = self.metadata[i] if i < len(self.metadata) else {}

                # 检查是否满足所有过滤条件
                matches = True

                # 检查实体类型条件
                if 'entity_types' in filter_conditions:
                    required_types = filter_conditions['entity_types']
                    example_types = metadata.get('entity_types', [])
                    # 要求至少包含一个指定的实体类型
                    if not any(etype in example_types for etype in required_types):
                        matches = False

                # 检查复杂度因子条件
                if 'complexity_factors' in filter_conditions and matches:
                    required_factors = filter_conditions['complexity_factors']
                    factor_checks = {
                        'abbreviations': 'has_abbreviations',
                        'numbers': 'has_numbers',
                        'passive_voice': 'has_passive_voice'
                    }

                    for factor in required_factors:
                        metadata_key = factor_checks.get(factor)
                        if metadata_key and not metadata.get(metadata_key, False):
                            matches = False
                            break

                if matches:
                    filtered_examples.append(example)

            logger.debug(f"🔍 元数据过滤: {len(self.examples)} -> {len(filtered_examples)}")
            return filtered_examples

        except Exception as e:
            logger.error(f"元数据过滤失败: {e}")
            return self.examples.copy()

    def similarity_search_in_subset(self, query_embedding: List[float],
                                   subset: List[Dict[str, Any]],
                                   top_k: int = 10) -> List[Dict[str, Any]]:
        """🔍 在指定子集中进行向量相似度搜索"""
        try:
            if not subset or not query_embedding:
                return []

            query_vec = np.array(query_embedding)
            scored_examples = []

            for example in subset:
                # 获取示例的嵌入向量
                example_embedding = example.get('embedding')
                if example_embedding is None:
                    continue

                example_vec = np.array(example_embedding)

                # 计算余弦相似度
                similarity = np.dot(example_vec, query_vec) / (
                    np.linalg.norm(example_vec) * np.linalg.norm(query_vec)
                )

                scored_examples.append({
                    **example,
                    'score': float(similarity)
                })

            # 按相似度排序并返回top_k
            scored_examples.sort(key=lambda x: x.get('score', 0.0), reverse=True)

            return scored_examples[:top_k]

        except Exception as e:
            logger.error(f"子集向量搜索失败: {e}")
            return subset[:top_k]


class ExampleRetriever:
    """简单的NER任务示例检索器"""

    def __init__(self):
        self.config = CONFIG.get('retrieval_config', {})
        self.model_service = model_service
        self.vector_store = SimpleVectorStore()
        self.initialized = False
        
        logger.info("示例检索器初始化完成")

    async def generate_dynamic_criteria(self, query: str) -> RetrievalCriteria:
        """使用LLM生成检索标准"""
        try:
            # 简单查询使用基于规则的快速回退
            query_complexity = self._assess_query_complexity(query)

            if query_complexity == "simple":
                logger.info(f"对简单查询使用基于规则的标准: '{query[:50]}...'")
                return self._generate_fallback_criteria(query)

            logger.info(f"为复杂查询生成LLM标准: '{query[:50]}...'")

            # 为LLM准备简单提示词
            prompt = f"""Query: "{query}"
Generate retrieval criteria:
1. Entity types (person/org/location/date/money)
2. Complexity (simple/medium/complex)
3. Domain (business/tech/general)
4. Reasoning (brief)"""

            messages = [{"role": "user", "content": prompt}]

            response = await model_service.generate_with_tools_async(
                messages=messages,
                tools=[RetrievalCriteria]
            )

            if response and response.tool_calls:
                tool_call = response.tool_calls[0]
                if tool_call.function and tool_call.function.arguments:
                    try:
                        criteria = RetrievalCriteria.model_validate_json(tool_call.function.arguments)
                        logger.info(f"生成LLM标准: {criteria.reasoning[:50]}...")
                        return criteria
                    except Exception as e:
                        logger.warning(f"解析LLM标准失败: {e}")

            return self._generate_fallback_criteria(query)

        except Exception as e:
            logger.warning(f"动态标准生成失败: {e}")
            return self._generate_fallback_criteria(query)

    def _assess_query_complexity(self, query: str) -> str:
        """评估查询复杂度"""
        word_count = len(query.split())
        entity_indicators = sum(1 for word in query.split() if word[0].isupper())

        if word_count <= 8 and entity_indicators >= 2:
            return "simple"
        if word_count > 15 or "?" in query:
            return "complex"
        return "medium"

    def _generate_fallback_criteria(self, query: str) -> RetrievalCriteria:
        """基于规则生成回退标准"""
        query_lower = query.lower()
        word_count = len(query.split())

        # 推断实体类型
        target_entities = []
        if any(word in query_lower for word in ['company', 'corp', 'inc', 'organization']):
            target_entities.append('organization')
        if any(word in query_lower for word in ['person', 'ceo', 'president']):
            target_entities.append('person')
        if any(word in query_lower for word in ['city', 'country', 'location']):
            target_entities.append('location')

        if not target_entities:
            target_entities = ['person', 'organization', 'location']

        if word_count < 8:
            complexity = 'simple'
        elif word_count < 15:
            complexity = 'medium'
        else:
            complexity = 'complex'
        domain = 'general'

        return RetrievalCriteria(
            target_entity_types=target_entities,
            text_complexity=complexity,
            domain_focus=domain,
            structural_preferences=[],
            difficulty_level='medium',
            reasoning=f"基于规则的分析: {len(target_entities)}个实体类型, {complexity}复杂度"
        )

    async def _load_pkl_cache(self, pkl_file: str, data_path: str) -> bool:
        """加载pkl缓存"""
        try:
            with open(pkl_file, 'rb') as f:
                cached_data = pickle.load(f)

            # 验证缓存
            if not self._validate_cache(cached_data, data_path):
                logger.warning("缓存验证失败，重新生成...")
                return False

            examples = cached_data['examples']
            embeddings = cached_data['embeddings']
            self.vector_store.add_examples(examples, embeddings)

            logger.info(f"从pkl缓存加载 {len(examples)} 个示例")
            self.initialized = True
            return True

        except Exception as e:
            logger.warning(f"加载pkl缓存失败: {e}")
            return False

    async def _migrate_json_to_pkl(self, json_file: str, pkl_file: str, data_path: str) -> bool:
        """将JSON缓存迁移到pkl格式"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            # 添加版本信息
            pkl_data = {
                'examples': json_data['examples'],
                'embeddings': json_data['embeddings'],
                'dataset_path': data_path,
                'created_at': json_data.get('created_at', time.time()),
                'version': '1.0',
                'dataset_hash': self._get_dataset_hash(data_path)
            }

            # 保存为pkl格式
            with open(pkl_file, 'wb') as f:
                pickle.dump(pkl_data, f)

            # 删除旧的JSON缓存
            os.remove(json_file)

            # 加载到向量存储
            self.vector_store.add_examples(pkl_data['examples'], pkl_data['embeddings'])
            logger.info(f"成功迁移并加载 {len(pkl_data['examples'])} 个示例")
            self.initialized = True
            return True

        except Exception as e:
            logger.warning(f"迁移JSON缓存失败: {e}")
            return False

    def _validate_cache(self, cached_data: Dict[str, Any], data_path: str) -> bool:
        """验证缓存有效性"""
        try:
            # 检查必需字段
            required_fields = ['examples', 'embeddings', 'dataset_path']
            if not all(field in cached_data for field in required_fields):
                return False

            # 检查数据集是否已更改
            if cached_data['dataset_path'] != data_path:
                return False

            # 检查数据集文件哈希（如果存在）
            if 'dataset_hash' in cached_data:
                current_hash = self._get_dataset_hash(data_path)
                if cached_data['dataset_hash'] != current_hash:
                    logger.info("数据集文件已更改，缓存失效")
                    return False

            return True

        except Exception:
            return False

    def _get_dataset_hash(self, data_path: str) -> str:
        """获取数据集文件哈希"""
        try:
            with open(data_path, 'rb') as f:
                content = f.read()
            return hashlib.md5(content).hexdigest()
        except Exception:
            return ""

    async def _generate_embeddings_in_batches(self, texts: List[str], batch_size: int = 50, max_concurrent: int = 5) -> List[List[float]]:
        """🚀 并发批量生成嵌入向量，大幅提升效率"""
        total_batches = (len(texts) + batch_size - 1) // batch_size

        logger.info(f"🚀 开始并发生成嵌入向量: {len(texts)} 个文本, {total_batches} 个批次, 最大并发: {max_concurrent}")

        # 创建批次任务
        batch_tasks = []
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_num = i // batch_size + 1
            batch_tasks.append(self._process_single_batch(batch_texts, batch_num, total_batches))

        # 使用信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_with_semaphore(task):
            async with semaphore:
                return await task

        # 并发执行所有批次
        try:
            batch_results = await asyncio.gather(*[process_with_semaphore(task) for task in batch_tasks])

            # 合并结果
            all_embeddings = []
            for batch_embeddings in batch_results:
                if batch_embeddings:
                    all_embeddings.extend(batch_embeddings)
                else:
                    logger.error("某个批次嵌入生成失败")
                    return []

            logger.info(f"✅ 并发嵌入生成完成: {len(all_embeddings)} 个向量")
            return all_embeddings

        except Exception as e:
            logger.error(f"❌ 并发嵌入生成失败: {e}")
            return []

    async def _process_single_batch(self, batch_texts: List[str], batch_num: int, total_batches: int) -> List[List[float]]:
        """处理单个批次的嵌入生成"""
        try:
            logger.info(f"🔄 处理批次 {batch_num}/{total_batches}: {len(batch_texts)} 个文本")
            batch_embeddings = await self.model_service.get_embeddings_async(batch_texts)
            if batch_embeddings:
                logger.info(f"✅ 批次 {batch_num} 完成")
                return batch_embeddings
            else:
                logger.error(f"❌ 批次 {batch_num} 嵌入生成失败")
                return []
        except Exception as e:
            logger.error(f"❌ 批次 {batch_num} 处理失败: {e}")
            return []

    async def _generate_and_cache_vectors(self, data_path: str, pkl_cache_file: str) -> bool:
        """生成向量并保存到pkl缓存"""
        try:
            # 加载数据集
            with open(data_path, 'r', encoding='utf-8') as f:
                examples = json.load(f)

            # 批量生成语义向量（避免API超时）
            texts = [example.get('text', '') for example in examples]
            embeddings = await self._generate_embeddings_in_batches(texts, batch_size=50)

            if not embeddings or len(embeddings) != len(examples):
                logger.error(f"嵌入生成失败: 预期 {len(examples)}, 实际 {len(embeddings) if embeddings else 0}")
                return False

            # 添加到向量存储
            self.vector_store.add_examples(examples, embeddings)

            # 保存到pkl缓存
            cache_data = {
                'examples': examples,
                'embeddings': embeddings,
                'dataset_path': data_path,
                'created_at': time.time(),
                'version': '1.0',
                'dataset_hash': self._get_dataset_hash(data_path)
            }

            with open(pkl_cache_file, 'wb') as f:
                pickle.dump(cache_data, f)

            logger.info(f"向量存储初始化完成，生成并缓存 {len(examples)} 个示例")
            self.initialized = True
            return True

        except Exception as e:
            logger.error(f"生成向量缓存失败: {e}")
            return False

    async def initialize_vector_store(self, data_path: Optional[str] = None) -> bool:
        """🔍 KISS原则：检测有无向量库，有则继续，没有就生成"""
        try:
            # 获取数据集路径
            if data_path is None:
                data_path = get_current_dataset_path()

            if not os.path.exists(data_path):
                logger.warning(f"数据集文件不存在: {data_path}")
                return False

            # 简单的缓存检测
            cache_dir = CONFIG.get('vector_cache_dir', './cache/vector')
            os.makedirs(cache_dir, exist_ok=True)

            # 🔍 修复：包含数据集名称标识，避免不同数据集混淆
            from config import CONFIG as GLOBAL_CONFIG
            current_dataset_key = GLOBAL_CONFIG.get('current_dataset', 'unknown')
            file_name = os.path.basename(data_path).replace('.json', '')
            pkl_cache_file = os.path.join(cache_dir, f"{current_dataset_key}_{file_name}_vectors.pkl")

            # 检测向量库是否存在
            if os.path.exists(pkl_cache_file):
                logger.info("🔍 检测到现有向量库，正在加载...")
                if await self._load_pkl_cache(pkl_cache_file, data_path):
                    logger.info("✅ 向量库加载成功")
                    return True
                else:
                    logger.warning("⚠️ 向量库加载失败，重新生成...")

            # 向量库不存在或损坏，生成新的
            logger.info("🚀 向量库不存在，开始生成...")
            success = await self._generate_and_cache_vectors(data_path, pkl_cache_file)
            if success:
                logger.info("✅ 向量库生成完成")
            return success

        except Exception as e:
            logger.error(f"向量库初始化失败: {e}")
            return False

    def smart_filter(self, criteria: RetrievalCriteria, max_candidates: int = 1000) -> List[Dict[str, Any]]:
        """智能过滤：基于LLM生成的动态检索标准"""
        try:
            if not self.initialized:
                logger.warning("向量存储未初始化，返回空结果")
                return []

            candidates = []
            logger.info(f"使用动态标准: {criteria.reasoning[:50]}...")

            for i, (example, metadata) in enumerate(zip(self.vector_store.examples, self.vector_store.metadata)):
                # 动态实体类型匹配
                if criteria.target_entity_types:
                    example_entities = set(metadata['entity_types'])
                    target_entities = set(criteria.target_entity_types)

                    # 至少一个目标实体类型
                    if not (target_entities & example_entities):
                        continue

                # 文本复杂度匹配
                text = example.get('text', '')
                text_length = len(text.split())

                if criteria.text_complexity == 'simple' and text_length > 15:
                    continue
                elif criteria.text_complexity == 'complex' and text_length < 10:
                    continue
                elif criteria.text_complexity == 'medium' and (text_length < 5 or text_length > 25):
                    continue

                # 领域焦点匹配
                if criteria.domain_focus != 'general':
                    example_domain = self._infer_domain(text)
                    if example_domain != criteria.domain_focus and example_domain != 'general':
                        continue

                # 结构偏好匹配
                if criteria.structural_preferences:
                    if 'multiple_entities' in criteria.structural_preferences:
                        entity_count = sum(len(entities) for entities in example.get('label', {}).values())
                        if entity_count < 2:
                            continue

                    if 'short_sentences' in criteria.structural_preferences:
                        if len(text) > 100:
                            continue
                
                candidates.append({
                    **example,
                    'metadata': metadata,
                    'index': i
                })
                
                if len(candidates) >= max_candidates:
                    break
            
            logger.info(f"智能过滤完成: 过滤 {len(candidates)} 个示例")
            return candidates

        except Exception as e:
            logger.error(f"智能过滤失败: {e}")
            return []

    def _infer_domain(self, text: str) -> str:
        """推断文本领域"""
        text_lower = text.lower()

        # 商业领域关键词
        business_keywords = ['company', 'corporation', 'business', 'market', 'financial', 'revenue', 'profit', 'investment']
        if any(keyword in text_lower for keyword in business_keywords):
            return 'business'

        # 技术领域关键词
        tech_keywords = ['technology', 'software', 'computer', 'internet', 'digital', 'ai', 'algorithm']
        if any(keyword in text_lower for keyword in tech_keywords):
            return 'technology'

        # 政治领域关键词
        politics_keywords = ['government', 'president', 'minister', 'parliament', 'election', 'policy']
        if any(keyword in text_lower for keyword in politics_keywords):
            return 'politics'

        return 'general'

    async def vector_retrieval(self, query: str, candidates: List[Dict[str, Any]], top_k: int = 20) -> List[Dict[str, Any]]:
        """向量检索：基于语义相似度进行检索"""
        try:
            if not candidates:
                logger.warning("没有候选示例进行向量检索")
                return []
            
            # 生成查询嵌入
            query_embedding = await self._generate_query_embedding(query)
            if not query_embedding:
                logger.warning("查询嵌入生成失败，使用回退")
                return candidates[:top_k]
            
            # 计算每个候选示例的相似度
            for candidate in candidates:
                candidate_embedding = candidate.get('embedding', [])
                if candidate_embedding:
                    similarity = self._cosine_similarity(query_embedding, candidate_embedding)
                    candidate['similarity_score'] = similarity
                else:
                    candidate['similarity_score'] = 0.0
            
            # 按相似度排序
            sorted_candidates = sorted(candidates, key=lambda x: x['similarity_score'], reverse=True)
            top_results = sorted_candidates[:top_k]
            
            logger.info(f"向量检索完成: 返回最相似的 {len(top_results)} 个示例")
            return top_results
            
        except Exception as e:
            logger.error(f"向量检索失败: {e}")
            return candidates[:top_k]  # 回退

    async def _generate_query_embedding(self, query: str) -> List[float]:
        """生成查询嵌入"""
        try:
            # 调用嵌入API
            embeddings = await self.model_service.get_embeddings_async([query])
            if embeddings and len(embeddings) > 0:
                return embeddings[0]
            return []
        except Exception as e:
            logger.error(f"生成查询嵌入失败: {e}")
            return []

    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        try:
            vec1_array = np.array(vec1)
            vec2_array = np.array(vec2)
            
            # 计算余弦相似度
            dot_product = np.dot(vec1_array, vec2_array)
            norm1 = np.linalg.norm(vec1_array)
            norm2 = np.linalg.norm(vec2_array)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
                
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
        except Exception as e:
            logger.error(f"计算余弦相似度失败: {e}")
            return 0.0

    async def reranker_refinement(self, query: str, candidates: List[Dict[str, Any]], top_k: int = 5) -> List[Dict[str, Any]]:
        """重排器优化：使用SiliconFlow重排器API优化相关性"""
        try:
            if not candidates:
                logger.warning("没有候选示例进行重排")
                return []
            
            if len(candidates) <= top_k:
                logger.info(f"候选数量 ({len(candidates)}) 不大于 top_k ({top_k})，跳过重排")
                return candidates
            
            # 准备重排器输入
            doc_texts = []
            for candidate in candidates:
                doc_text = f"Text: {candidate.get('text', '')}\nEntities: {candidate.get('label', {})}"
                doc_texts.append(doc_text)
            
            # 调用重排器API
            rerank_result = await self.model_service.rerank_async(
                query=query,
                documents=doc_texts,
                top_k=top_k
            )
            
            if not rerank_result:
                logger.warning("重排器未返回结果，返回原始顺序")
                return candidates[:top_k]
            
            # 根据重排结果重新排序
            reranked_candidates = []
            for i, rerank_item in enumerate(rerank_result):
                # 重排器返回结果按相关性排序，需要从原始candidates中获取对应项
                # 如果rerank_item包含index，使用它；否则使用位置索引
                index = rerank_item.get('index', i)
                if index < len(candidates):
                    candidate = candidates[index].copy()
                    candidate['rerank_score'] = rerank_item.get('score', 0.5)
                    reranked_candidates.append(candidate)
                else:
                    # 如果索引超出范围，使用位置索引
                    if i < len(candidates):
                        candidate = candidates[i].copy()
                        candidate['rerank_score'] = rerank_item.get('score', 0.5)
                        reranked_candidates.append(candidate)
            
            logger.info(f"重排器优化完成: 返回最相关的 {len(reranked_candidates)} 个示例")
            return reranked_candidates
            
        except Exception as e:
            logger.error(f"重排器优化失败: {e}")
            return candidates[:top_k]  # 回退

    async def execute_retrieval(self, query: str) -> List[Dict[str, Any]]:
        """执行完整的LLM驱动检索过程"""
        logger.info(f"开始完整的LLM驱动检索过程，查询: '{query[:50]}...'")

        # 步骤1：生成动态检索标准
        criteria = await self.generate_dynamic_criteria(query)

        # 步骤2：智能过滤
        candidates = self.smart_filter(criteria, max_candidates=1000)
        if not candidates:
            logger.warning("智能过滤未返回结果")
            return []
        
        # 步骤3：向量检索
        vector_results = await self.vector_retrieval(query, candidates, top_k=20)
        if not vector_results:
            logger.warning("向量检索未返回结果")
            return []

        # 步骤4：重排器优化
        final_results = await self.reranker_refinement(query, vector_results, top_k=5)
        
        # 修复：转换为ScoredExample对象
        from schemas import ScoredExample
        scored_results = []
        for i, result in enumerate(final_results):
            # 综合分数：重排分数 * 0.7 + 相似度分数 * 0.3
            rerank_score = result.get('rerank_score', 0.5)
            similarity_score = result.get('similarity_score', 0.5)
            final_score = rerank_score * 0.7 + similarity_score * 0.3

            scored_example = ScoredExample(
                example=result,
                relevance_score=final_score,
                source="retrieval_pipeline",
                metadata={"rank": i + 1, "rerank_score": rerank_score, "similarity_score": similarity_score}
            )
            scored_results.append(scored_example)
        
        logger.info(f"检索过程完成: 返回 {len(scored_results)} 个精选示例")
        return scored_results



    def _filter_by_required_features(self, required_features: Dict) -> List[Dict[str, Any]]:
        """根据事实标签过滤候选示例"""
        if not self.initialized:
            return []
        
        candidates = []
        for i, (example, metadata) in enumerate(zip(self.vector_store.examples, self.vector_store.metadata)):
            # 检查实体类型匹配
            if 'entity_types' in required_features:
                required_entities = set(required_features['entity_types'])
                example_entities = set(metadata.get('entity_types', []))
                if not (required_entities & example_entities):
                    continue
            
            # 检查结构标签匹配（如果需要）
            if 'structural_tags' in required_features and required_features['structural_tags']:
                required_tags = set(required_features['structural_tags'])
                example_tags = set()
                if metadata.get('has_passive_voice'):
                    example_tags.add('passive_voice')
                if metadata.get('has_numbers'):
                    example_tags.add('has_numbers')
                if metadata.get('has_abbreviations'):
                    example_tags.add('has_abbreviations')

                # 只有当需要结构标签且没有匹配时才跳过
                if required_tags and not (required_tags & example_tags):
                    continue
            
            candidates.append({
                **example,
                'metadata': metadata,
                'index': i
            })
        
        return candidates

    async def _semantic_search_by_description(self, description: str, candidates: List[Dict], top_k: int = 20) -> List[Dict]:
        """基于描述进行语义检索"""
        if not candidates:
            return []
        
        # 生成描述嵌入
        description_embedding = await self._generate_query_embedding(description)
        if not description_embedding:
            return candidates[:top_k]
        
        # 计算相似度
        for candidate in candidates:
            candidate_embedding = candidate.get('embedding', [])
            if candidate_embedding:
                similarity = self._cosine_similarity(description_embedding, candidate_embedding)
                candidate['semantic_score'] = similarity
            else:
                candidate['semantic_score'] = 0.0
        
        # 按相似度排序
        sorted_candidates = sorted(candidates, key=lambda x: x.get('semantic_score', 0), reverse=True)
        return sorted_candidates[:top_k]


# 全局单例
example_retriever = ExampleRetriever()
