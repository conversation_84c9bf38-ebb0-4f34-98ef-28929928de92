#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 APIICL - 元认知智能体NER系统
专注于数据集处理和评估的简化版本
"""

import asyncio
import argparse
import logging
import json

import os
from typing import Dict, Any, Optional
from datetime import datetime

from config import CONFIG, set_dataset, list_available_datasets, get_current_dataset_info, initialize_datasets
from pipeline import run_meta_cognitive_ner_pipeline


def setup_logging(level: str = "WARNING"):
    """设置日志配置"""
    import sys
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S',
        stream=sys.stderr  # 确保日志不干扰tqdm
    )



def print_banner():
    """打印系统横幅"""
    print("=" * 60)
    print("🧠 APIICL - 元认知智能体NER系统")
    print("📚 数据集处理和评估模式")
    print("=" * 60)


async def process_and_eval_dataset(max_samples: Optional[int] = None) -> Dict[str, Any]:
    """🎯 处理数据集并进行评估"""

    # 获取当前数据集信息
    current_dataset = get_current_dataset_info()
    dataset_path = current_dataset['path']

    # 检查测试集文件
    test_path = dataset_path.replace('train.json', 'test.json')
    if not os.path.exists(test_path):
        print(f"❌ 测试集文件不存在: {test_path}")
        return {}

    # 数据准备和向量库预初始化
    print("📚 数据准备和向量库预初始化")
    try:
        with open(test_path, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
    except Exception as e:
        print(f"❌ 加载测试集失败: {e}")
        return {}

    # 限制样本数量
    if max_samples and max_samples < len(test_data):
        test_data = test_data[:max_samples]

    # 🔍 KISS原则：预先初始化向量库，避免重复初始化
    print("🔍 预初始化向量库...")
    from example_retriever import ExampleRetriever
    global_retriever = ExampleRetriever()
    vector_ready = await global_retriever.initialize_vector_store()
    if vector_ready:
        print("✅ 向量库预初始化成功")
    else:
        print("⚠️ 向量库预初始化失败，将使用直接模式")

    print("✅ 数据准备完成")

    # 🛠️ 修复：使用预初始化向量库，避免重复初始化导致的并发爆炸
    # 现在可以安全使用更高并发数，因为向量库已预初始化
    max_concurrent = CONFIG.get('max_concurrent_requests', 200)  # 恢复较高并发数
    semaphore_limit = min(max_concurrent, len(test_data))
    semaphore = asyncio.Semaphore(semaphore_limit)

    async def process_single_sample_with_semaphore(sample, sample_id):
        """带信号量控制的单样本处理 - 使用预初始化的向量库"""
        async with semaphore:
            try:
                text = sample.get('text', '')
                # 🔍 使用预初始化的向量库，避免重复初始化
                predicted_labels = await run_meta_cognitive_ner_pipeline(text, pre_initialized_retriever=global_retriever)
                return sample_id, sample, predicted_labels, None
            except Exception as e:
                return sample_id, sample, {}, e

    # 创建所有任务
    all_tasks = [
        process_single_sample_with_semaphore(sample, i)
        for i, sample in enumerate(test_data)
    ]

    results = []
    correct_predictions = 0
    total_entities = 0
    predicted_entities = 0

    # 🔧 修复：简化进度显示，避免并发显示问题
    print("🚀 开始模型推理...")

    # 批量执行并收集结果
    task_results = []
    completed_count = 0
    for completed_task in asyncio.as_completed(all_tasks):
        result = await completed_task
        task_results.append(result)
        completed_count += 1

        # 每10个任务显示一次进度
        if completed_count % 10 == 0 or completed_count == len(all_tasks):
            progress = completed_count / len(all_tasks) * 100
            print(f"📊 进度: {completed_count}/{len(all_tasks)} ({progress:.1f}%)")

    # 处理结果
    for _, sample, predicted_labels, error in task_results:
        text = sample.get('text', '')
        true_labels = sample.get('label', {})

        if error:
            predicted_labels = {}
        elif not isinstance(predicted_labels, dict):
            predicted_labels = {}

        # 计算指标
        sample_correct = 0
        sample_total = sum(len(entities) for entities in true_labels.values())
        sample_predicted = sum(len(entities) for entities in predicted_labels.values())

        for entity_type, true_entities in true_labels.items():
            predicted_entities_of_type = predicted_labels.get(entity_type, [])
            for entity in true_entities:
                if entity in predicted_entities_of_type:
                    sample_correct += 1

        correct_predictions += sample_correct
        total_entities += sample_total
        predicted_entities += sample_predicted

        results.append({
            'text': text,
            'true_labels': true_labels,
            'predicted_labels': predicted_labels,
            'correct': sample_correct,
            'total_true': sample_total,
            'total_predicted': sample_predicted
        })

    # 计算最终指标
    precision = correct_predictions / predicted_entities if predicted_entities > 0 else 0
    recall = correct_predictions / total_entities if total_entities > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    # 显示最终评估结果
    print("\n" + "="*60)
    print("🎯 最终评估结果")
    print("="*60)
    print(f"📊 数据集: {current_dataset['name']}")
    print(f"📝 处理样本数: {len(test_data)}")
    print(f"🎯 真实实体总数: {total_entities}")
    print(f"🔍 预测实体总数: {predicted_entities}")
    print(f"✅ 正确预测数: {correct_predictions}")
    print("-" * 40)
    print(f"📈 Precision: {precision:.4f} ({correct_predictions}/{predicted_entities})")
    print(f"📈 Recall: {recall:.4f} ({correct_predictions}/{total_entities})")
    print(f"📈 F1-Score: {f1_score:.4f}")
    print("="*60)
    
    # 保存评估结果
    eval_results = {
        'dataset': current_dataset['name'],
        'timestamp': datetime.now().isoformat(),
        'samples_count': len(test_data),
        'total_entities': total_entities,
        'predicted_entities': predicted_entities,
        'correct_predictions': correct_predictions,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'detailed_results': results
    }
    
    # 保存到文件 - 使用results文件夹
    results_dir = "results"
    os.makedirs(results_dir, exist_ok=True)  # 确保results目录存在
    eval_file = os.path.join(results_dir, f"eval_results_{current_dataset['name'].lower().replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    try:
        with open(eval_file, 'w', encoding='utf-8') as f:
            json.dump(eval_results, f, ensure_ascii=False, indent=2)
        print(f"💾 详细结果已保存到: {eval_file}")
    except Exception as e:
        print(f"⚠️ 保存结果失败: {e}")
    
    return eval_results


async def main():
    """主函数 - 数据集处理和评估"""
    parser = argparse.ArgumentParser(description='🧠 APIICL - 元认知智能体NER系统')
    parser.add_argument('--dataset', '-d', type=str, default='ace2005', 
                       help='数据集名称 (默认: ace2005)')
    parser.add_argument('--max-samples', type=int, 
                       help='最大测试样本数 (默认: 处理全部)')
    parser.add_argument('--log-level', type=str, default='WARNING', 
                       help='日志级别 (DEBUG/INFO/WARNING/ERROR)')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)

    # 初始化数据集配置（智能检测标签集）
    initialize_datasets()

    # 打印横幅
    print_banner()
    
    # 设置数据集
    if not set_dataset(args.dataset):
        print(f"❌ 数据集不存在: {args.dataset}")
        available = list_available_datasets()
        print("\n可用数据集:")
        for key, info in available.items():
            status = "✅" if info['available'] else "❌"
            current = "👈 当前" if info['current'] else ""
            print(f"  {status} {key}: {info['name']} {current}")
        return
    
    # 显示当前配置
    current_dataset = get_current_dataset_info()
    print(f"📊 数据集: {current_dataset['name']}")
    if args.max_samples:
        print(f"📝 最大样本数: {args.max_samples}")
    print()
    
    try:
        # 直接进行数据集处理和评估
        await process_and_eval_dataset(args.max_samples)
        
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
