#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 元认知智能体 - 基于单一超级Prompt的NER系统
核心理念：LLM一次性完成思考、决策和执行
遵循KISS原则：简单、高效、优雅
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional

from config import CONFIG, get_current_dataset_info
from model_interface import model_service
from schemas import RetrieveFewShotTool, ExtractionTool

logger = logging.getLogger(__name__)


class MetaCognitiveAgent:
    """🧠 元认知智能体 - 单一超级Prompt架构的核心引擎"""
    
    def __init__(self, example_retriever=None):
        """
        初始化元认知智能体
        
        Args:
            example_retriever: 预初始化的示例检索器
        """
        self.example_retriever = example_retriever
        self.model_service = model_service
        
    def _get_current_entity_types(self) -> List[str]:
        """获取当前数据集的实体类型"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('labels', ['person', 'organization', 'location'])
    
    def _get_current_label_prompt(self) -> str:
        """获取当前数据集的标签提示"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('label_prompt', '')
    
    def _build_super_prompt(self, text: str) -> str:
        """
        🚀 构建单一超级Prompt - 包含完整的NER逻辑和工具说明
        
        这是整个系统的大脑，LLM将通过这个Prompt一次性完成：
        1. 分析文本复杂度和实体模式
        2. 决定是否需要示例辅助
        3. 如需要，生成具体的检索需求
        4. 调用相应的工具完成任务
        """
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)
        label_prompt = self._get_current_label_prompt()
        
        super_prompt = f"""You are an expert Named Entity Recognition system with access to few-shot examples.

Your task: Extract named entities from the given text using these entity types: {entity_types_str}

{label_prompt}

Process:
1. First, analyze the input text and decide how many few-shot examples you need (minimum 1, maximum 10)
2. Call retrieve_few_shot to get the examples
3. Then call extract_entities to perform the final NER

Input text to analyze: "{text}"

Guidelines:
- Always request at least 1 example (no direct NER without examples)
- For simple texts: 1-2 examples
- For complex texts with domain terms, abbreviations, or ambiguous boundaries: 3-5 examples
- For very challenging texts: 5-10 examples

You MUST call both tools in sequence: retrieve_few_shot → extract_entities"""

        return super_prompt
    
    async def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """
        🚀 主要入口：使用单一超级Prompt进行NER
        
        核心创新：
        1. LLM一次性接收超级Prompt
        2. LLM自主决策是否需要检索示例
        3. 如需要，LLM生成具体的检索需求
        4. 系统执行检索并返回结果
        5. LLM整合示例和原文，完成最终NER
        """
        try:
            # 🚀 步骤1：构建并发送超级Prompt
            super_prompt = self._build_super_prompt(text)
            
            # 🧠 步骤2：LLM分析并决策工具调用
            tools = [RetrieveFewShotTool, ExtractionTool]
            messages = [{"role": "user", "content": super_prompt}]
            
            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )
            
            # 🔍 步骤3：处理LLM的工具调用决策
            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                return await self._handle_tool_calls(response.tool_calls, text)
            else:
                logger.warning("LLM未调用任何工具，使用直接NER作为fallback")
                return await self._perform_direct_ner(text)
                
        except Exception as e:
            logger.error(f"元认知智能体NER失败: {e}")
            return {}
    
    async def _handle_tool_calls(self, tool_calls: List[Any], original_text: str) -> Dict[str, List[str]]:
        """
        🛠️ 处理LLM的工具调用序列
        
        LLM可能的调用模式：
        1. 直接调用extract_entities（简单文本）
        2. 先调用retrieve_ner_examples，再调用extract_entities（复杂文本）
        """
        retrieved_examples = []
        final_entities = {}
        
        for tool_call in tool_calls:
            if not tool_call.function:
                continue
                
            function_name = tool_call.function.name
            
            try:
                arguments = json.loads(tool_call.function.arguments)
            except json.JSONDecodeError as e:
                logger.error(f"解析工具参数失败: {e}")
                continue
            
            if function_name == "RetrieveFewShotTool":
                # 🔍 LLM决定需要few-shot示例
                retrieved_examples = await self._execute_few_shot_retrieval(arguments, original_text)
                logger.info(f"✅ 检索到 {len(retrieved_examples)} 个示例")
                
            elif function_name == "ExtractionTool":
                # 🎯 LLM执行最终的实体提取
                entities = arguments.get("entities", {})
                final_entities = entities
                logger.info(f"✅ 提取到 {sum(len(v) for v in entities.values())} 个实体")
        
        # 🚀 简化处理：必须有示例才能进行NER
        if not final_entities:
            if retrieved_examples:
                # 使用检索到的示例进行NER
                final_entities = await self._perform_ner_with_examples(original_text, retrieved_examples)
            else:
                # 没有示例就报错，因为按新逻辑必须要有示例
                logger.error("❌ 没有检索到示例，无法进行NER")
                final_entities = {}

        return final_entities
    
    async def _execute_few_shot_retrieval(self, retrieval_args: Dict[str, Any], original_text: str) -> List[Dict[str, Any]]:
        """
        🔍 执行few-shot示例检索 - 简化版本
        """
        try:
            num_examples = retrieval_args.get("num_examples", 3)
            description = retrieval_args.get("description", "")

            logger.info(f"🔍 需要 {num_examples} 个示例")

            if not self.example_retriever or not self.example_retriever.initialized:
                logger.warning("⚠️ 示例检索器未初始化")
                return []

            # 🔍 简单的语义检索
            search_query = f"{original_text} {description}".strip()
            query_embedding = await self.example_retriever._generate_query_embedding(search_query)

            if not query_embedding:
                return []

            # 检索候选示例（多检索一些用于重排）
            candidates = self.example_retriever.vector_store.similarity_search(
                query_embedding, top_k=num_examples * 3
            )

            # 重排器精排
            if candidates:
                final_examples = await self.example_retriever.reranker_refinement(
                    search_query, candidates, top_k=num_examples
                )
            else:
                final_examples = []

            logger.info(f"✅ 检索到 {len(final_examples)} 个示例")
            return final_examples

        except Exception as e:
            logger.error(f"Few-shot检索失败: {e}")
            return []

    async def _multi_path_retrieval(self, request: Dict[str, Any], original_text: str) -> List[Dict[str, Any]]:
        """
        🚀 多路并行检索 - 实现用户方案中的"弹性加权"检索策略

        路径A (语义优先): 忽略标签，大规模向量搜索，保证召回率
        路径B (标签辅助): 硬性过滤+语义搜索，提升精确度
        智能融合: 综合信任分 = 语义分(高权重) + 标签分(中权重) + 语法分(低权重)
        """
        try:
            description = request.get("description", "")
            required_entity_types = request.get("required_entity_types", [])
            domain_context = request.get("domain_context", "")
            complexity_factors = request.get("complexity_factors", [])

            # 构建搜索查询
            search_query = f"{original_text} {description} {domain_context}".strip()

            # 🔍 路径A: 语义优先检索（保证召回率）
            path_a_candidates = await self._semantic_priority_retrieval(search_query, top_k=50)

            # 🔍 路径B: 标签辅助检索（提升精确度）
            path_b_candidates = await self._label_assisted_retrieval(
                search_query, required_entity_types, complexity_factors, top_k=20
            )

            # 🚀 智能融合与加权打分
            merged_candidates = self._merge_and_score_candidates(
                path_a_candidates, path_b_candidates
            )

            # 🎯 重排器最终精排
            if merged_candidates:
                final_examples = await self.example_retriever.reranker_refinement(
                    search_query, merged_candidates, top_k=10
                )
            else:
                final_examples = []

            logger.info(f"🔍 多路检索完成: 路径A={len(path_a_candidates)}, 路径B={len(path_b_candidates)}, 最终={len(final_examples)}")
            return final_examples

        except Exception as e:
            logger.error(f"多路检索失败: {e}")
            return []

    async def _semantic_priority_retrieval(self, search_query: str, top_k: int = 50) -> List[Dict[str, Any]]:
        """🔍 路径A: 语义优先检索 - 忽略标签，保证召回率"""
        try:
            # 生成查询嵌入
            query_embedding = await self.example_retriever._generate_query_embedding(search_query)
            if not query_embedding:
                return []

            # 在整个知识库中进行大规模向量搜索
            candidates = self.example_retriever.vector_store.similarity_search(
                query_embedding, top_k=top_k
            )

            return candidates

        except Exception as e:
            logger.error(f"语义优先检索失败: {e}")
            return []

    async def _label_assisted_retrieval(self, search_query: str, required_entity_types: List[str],
                                      complexity_factors: List[str], top_k: int = 20) -> List[Dict[str, Any]]:
        """🔍 路径B: 标签辅助检索 - 硬性过滤+语义搜索，提升精确度"""
        try:
            if not required_entity_types and not complexity_factors:
                return []

            # 构建元数据过滤条件
            filter_conditions = {}
            if required_entity_types:
                filter_conditions["entity_types"] = required_entity_types
            if complexity_factors:
                filter_conditions["complexity_factors"] = complexity_factors

            # 先进行硬性过滤
            filtered_candidates = self.example_retriever.vector_store.filter_by_metadata(filter_conditions)

            if not filtered_candidates:
                return []

            # 在过滤后的候选中进行语义搜索
            query_embedding = await self.example_retriever._generate_query_embedding(search_query)
            if not query_embedding:
                return filtered_candidates[:top_k]

            # 在过滤后的范围内进行向量搜索
            candidates = self.example_retriever.vector_store.similarity_search_in_subset(
                query_embedding, filtered_candidates, top_k=top_k
            )

            return candidates

        except Exception as e:
            logger.error(f"标签辅助检索失败: {e}")
            return []

    def _merge_and_score_candidates(self, path_a_candidates: List[Dict[str, Any]],
                                  path_b_candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        🚀 智能融合与加权打分

        综合信任分 = w1 * 语义相似分 + w2 * 标签匹配加分 + w3 * 语法质量分
        """
        try:
            # 权重配置（根据用户方案）
            w1_semantic = 0.6  # 语义分权重最高
            w2_label = 0.3     # 标签分权重次之
            w3_grammar = 0.1   # 语法分权重最低

            # 合并去重
            all_candidates = {}

            # 处理路径A候选（语义优先）
            for candidate in path_a_candidates:
                text = candidate.get('text', '')
                if text not in all_candidates:
                    all_candidates[text] = {
                        'example': candidate,
                        'semantic_score': candidate.get('score', 0.0),
                        'from_path_b': False,
                        'grammar_score': candidate.get('readability_score', 0.5)
                    }

            # 处理路径B候选（标签辅助）
            for candidate in path_b_candidates:
                text = candidate.get('text', '')
                if text in all_candidates:
                    # 已存在，标记为来自路径B
                    all_candidates[text]['from_path_b'] = True
                else:
                    # 新候选
                    all_candidates[text] = {
                        'example': candidate,
                        'semantic_score': candidate.get('score', 0.0),
                        'from_path_b': True,
                        'grammar_score': candidate.get('readability_score', 0.5)
                    }

            # 计算综合信任分
            scored_candidates = []
            for text, info in all_candidates.items():
                semantic_score = info['semantic_score']
                label_bonus = 0.2 if info['from_path_b'] else 0.0  # 路径B加分
                grammar_score = info['grammar_score']

                # 综合信任分计算
                trust_score = (w1_semantic * semantic_score +
                             w2_label * label_bonus +
                             w3_grammar * grammar_score)

                candidate_with_score = info['example'].copy()
                candidate_with_score['trust_score'] = trust_score
                scored_candidates.append(candidate_with_score)

            # 按综合信任分排序
            scored_candidates.sort(key=lambda x: x.get('trust_score', 0.0), reverse=True)

            return scored_candidates[:50]  # 返回前50个用于重排器

        except Exception as e:
            logger.error(f"候选融合打分失败: {e}")
            return path_a_candidates + path_b_candidates  # 降级处理

    def _select_best_examples(self, all_examples: List[Dict[str, Any]], max_count: int = 5) -> List[Dict[str, Any]]:
        """🎯 选择最佳示例 - 全局汇总与多样性确保（MMR算法）"""
        try:
            if not all_examples:
                return []

            if len(all_examples) <= max_count:
                return all_examples

            # 简化的MMR算法：选择高分且多样化的示例
            selected = []
            remaining = all_examples.copy()

            # 首先选择得分最高的
            remaining.sort(key=lambda x: x.get('trust_score', x.get('score', 0.0)), reverse=True)
            selected.append(remaining.pop(0))

            # 迭代选择剩余示例，平衡相关性和多样性
            while len(selected) < max_count and remaining:
                best_candidate = None
                best_score = -1

                for candidate in remaining:
                    # 相关性分数
                    relevance_score = candidate.get('trust_score', candidate.get('score', 0.0))

                    # 多样性分数（简化：基于文本长度和实体类型差异）
                    diversity_score = self._calculate_diversity_score(candidate, selected)

                    # MMR分数：平衡相关性和多样性
                    mmr_score = 0.7 * relevance_score + 0.3 * diversity_score

                    if mmr_score > best_score:
                        best_score = mmr_score
                        best_candidate = candidate

                if best_candidate:
                    selected.append(best_candidate)
                    remaining.remove(best_candidate)
                else:
                    break

            logger.info(f"🎯 MMR选择完成: 从 {len(all_examples)} 个候选中选择了 {len(selected)} 个最佳示例")
            return selected

        except Exception as e:
            logger.error(f"示例选择失败: {e}")
            return all_examples[:max_count]

    def _calculate_diversity_score(self, candidate: Dict[str, Any], selected: List[Dict[str, Any]]) -> float:
        """计算候选示例与已选示例的多样性分数"""
        try:
            if not selected:
                return 1.0

            candidate_text = candidate.get('text', '')
            candidate_entities = set(candidate.get('label', {}).keys())
            candidate_length = len(candidate_text)

            diversity_scores = []

            for selected_example in selected:
                selected_text = selected_example.get('text', '')
                selected_entities = set(selected_example.get('label', {}).keys())
                selected_length = len(selected_text)

                # 文本长度差异
                length_diff = abs(candidate_length - selected_length) / max(candidate_length, selected_length, 1)

                # 实体类型差异
                entity_overlap = len(candidate_entities & selected_entities)
                entity_union = len(candidate_entities | selected_entities)
                entity_diversity = 1.0 - (entity_overlap / max(entity_union, 1))

                # 综合多样性分数
                diversity = (length_diff + entity_diversity) / 2
                diversity_scores.append(diversity)

            # 返回平均多样性分数
            return sum(diversity_scores) / len(diversity_scores)

        except Exception as e:
            logger.error(f"多样性分数计算失败: {e}")
            return 0.5

    async def _perform_ner_with_examples(self, original_text: str, examples: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """🎯 使用检索到的示例进行最终NER"""
        try:
            if not examples:
                # 无示例，直接进行NER
                return await self._perform_direct_ner(original_text)

            # 格式化示例为上下文
            examples_context = self._format_examples_for_context(examples)

            # 构建增强的NER Prompt
            enhanced_prompt = self._build_enhanced_ner_prompt(original_text, examples_context)

            # 执行NER
            tools = [ExtractionTool]
            messages = [{"role": "user", "content": enhanced_prompt}]

            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )

            # 解析结果
            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                tool_call = response.tool_calls[0]
                if tool_call.function and tool_call.function.arguments:
                    try:
                        arguments = json.loads(tool_call.function.arguments)
                        entities = arguments.get("entities", {})
                        return entities
                    except json.JSONDecodeError as e:
                        logger.error(f"解析NER结果失败: {e}")

            logger.warning("增强NER未返回有效结果，尝试直接NER")
            return await self._perform_direct_ner(original_text)

        except Exception as e:
            logger.error(f"示例辅助NER失败: {e}")
            return await self._perform_direct_ner(original_text)

    async def _perform_direct_ner(self, text: str) -> Dict[str, List[str]]:
        """🎯 直接进行NER（无示例辅助）"""
        try:
            entity_types = self._get_current_entity_types()
            entity_types_str = ', '.join(entity_types)
            label_prompt = self._get_current_label_prompt()

            direct_prompt = f"""You are an expert Named Entity Recognition system.

{label_prompt}

Extract named entities from the following text using these entity types: {entity_types_str}

Text to analyze: "{text}"

Extract all named entities and return them using the extract_entities tool."""

            tools = [ExtractionTool]
            messages = [{"role": "user", "content": direct_prompt}]

            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )

            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                tool_call = response.tool_calls[0]
                if tool_call.function and tool_call.function.arguments:
                    try:
                        arguments = json.loads(tool_call.function.arguments)
                        entities = arguments.get("entities", {})
                        return entities
                    except json.JSONDecodeError as e:
                        logger.error(f"解析直接NER结果失败: {e}")

            return {}

        except Exception as e:
            logger.error(f"直接NER失败: {e}")
            return {}

    def _format_examples_for_context(self, examples: List[Dict[str, Any]]) -> str:
        """将检索到的示例格式化为上下文"""
        if not examples:
            return "No examples available."

        formatted_examples = []
        for i, example in enumerate(examples, 1):
            text = example.get('text', '')
            labels = example.get('label', {})

            entities_str = ", ".join(
                f"'{entity}' ({etype})"
                for etype, entities in labels.items()
                for entity in entities
            )

            formatted_examples.append(f"Example {i}:\nText: {text}\nEntities: [{entities_str}]")

        return "\n\n".join(formatted_examples)

    def _build_enhanced_ner_prompt(self, text: str, examples_context: str) -> str:
        """构建包含示例的增强NER Prompt"""
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)
        label_prompt = self._get_current_label_prompt()

        return f"""You are an expert Named Entity Recognition system.

{label_prompt}

Learn from these carefully selected examples:
{examples_context}

Now extract named entities from this text using these entity types: {entity_types_str}

Text to analyze: "{text}"

Extract all named entities and return them using the extract_entities tool."""


# 🚀 全局实例管理
_meta_cognitive_agent = None

def get_meta_cognitive_agent(example_retriever=None):
    """获取元认知智能体实例"""
    global _meta_cognitive_agent
    if _meta_cognitive_agent is None:
        _meta_cognitive_agent = MetaCognitiveAgent(example_retriever)
    return _meta_cognitive_agent
