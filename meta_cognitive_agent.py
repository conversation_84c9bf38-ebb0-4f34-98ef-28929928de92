#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 元认知智能体 - 基于单一超级Prompt的NER系统
核心理念：LLM一次性完成思考、决策和执行
遵循KISS原则：简单、高效、优雅
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional

from config import CONFIG, get_current_dataset_info
from model_interface import model_service
from schemas import RetrieveNERExamplesTool, ExtractionTool, RetrievalRequest

logger = logging.getLogger(__name__)


class MetaCognitiveAgent:
    """🧠 元认知智能体 - 单一超级Prompt架构的核心引擎"""

    def __init__(self, example_retriever=None):
        """
        初始化元认知智能体

        Args:
            example_retriever: 预初始化的示例检索器
        """
        self.example_retriever = example_retriever
        self.model_service = model_service

    def _get_current_entity_types(self) -> List[str]:
        """获取当前数据集的实体类型"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('labels', ['person', 'organization', 'location'])

    def _get_current_label_prompt(self) -> str:
        """获取当前数据集的标签提示"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('label_prompt', '')

    def _build_super_prompt(self, text: str) -> str:
        """
        🚀 构建单一超级Prompt - 包含完整的NER逻辑和工具说明

        这是整个系统的大脑，LLM将通过这个Prompt一次性完成：
        1. 分析文本复杂度和实体模式
        2. 决定是否需要示例辅助
        3. 如需要，生成具体的检索需求
        4. 调用相应的工具完成任务
        """
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)
        label_prompt = self._get_current_label_prompt()

        super_prompt = f"""You are an expert NER task planner. Your job is to analyze text and generate intelligent retrieval orders.

Your task: Extract named entities from the given text using these entity types: {entity_types_str}

{label_prompt}

Available tools:
1. retrieve_ner_examples: Generate structured retrieval requests for specific NER difficulties
2. extract_entities: Extract entities using retrieved examples (call this as final step)

Process:
1. Analyze the input text for NER difficulties and challenges
2. For each difficulty, create a detailed retrieval_request with:
   - description: Detailed natural language description of the specific NER difficulty
   - required_entity_types: Optional entity types to help filter relevant examples
3. Call retrieve_ner_examples with your analysis
4. Then call extract_entities to perform final NER

Input text to analyze: "{text}"

Guidelines for difficulty analysis:
- Entity boundary ambiguity (e.g., "Apple Inc." vs "Apple")
- Domain-specific terminology that needs context
- Abbreviations or acronyms that could be entities
- Nested or overlapping entities
- Unclear entity types from context

Example retrieval_request:
{{
  "description": "Examples with brand names that look like person names, showing clear entity boundaries",
  "required_entity_types": ["organization", "person"]
}}

You MUST call both tools: retrieve_ner_examples → extract_entities"""

        return super_prompt

    async def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """
        🚀 主要入口：使用单一超级Prompt进行NER

        核心创新：
        1. LLM一次性接收超级Prompt
        2. LLM自主决策是否需要检索示例
        3. 如需要，LLM生成具体的检索需求
        4. 系统执行检索并返回结果
        5. LLM整合示例和原文，完成最终NER
        """
        try:
            # 🚀 步骤1：构建并发送超级Prompt
            super_prompt = self._build_super_prompt(text)

            # 🧠 步骤2：LLM分析并决策工具调用
            tools = [RetrieveNERExamplesTool, ExtractionTool]
            messages = [{"role": "user", "content": super_prompt}]

            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )

            # 🔍 步骤3：处理LLM的工具调用决策
            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                return await self._handle_tool_calls(response.tool_calls, text)
            else:
                logger.warning("LLM未调用任何工具，使用直接NER作为fallback")
                return await self._perform_direct_ner(text)

        except Exception as e:
            logger.error(f"元认知智能体NER失败: {e}")
            return {}

    async def _handle_tool_calls(self, tool_calls: List[Any], original_text: str) -> Dict[str, List[str]]:
        """
        🛠️ 处理LLM的工具调用序列

        LLM可能的调用模式：
        1. 直接调用extract_entities（简单文本）
        2. 先调用retrieve_ner_examples，再调用extract_entities（复杂文本）
        """
        retrieved_examples = []
        final_entities = {}

        for tool_call in tool_calls:
            if not tool_call.function:
                continue

            function_name = tool_call.function.name

            try:
                arguments = json.loads(tool_call.function.arguments)
            except json.JSONDecodeError as e:
                logger.error(f"解析工具参数失败: {e}")
                continue

            if function_name == "RetrieveNERExamplesTool":
                # 🔍 LLM生成智能订单
                retrieved_examples = await self._execute_retrieval(arguments, original_text)
                logger.info(f"✅ 检索到 {len(retrieved_examples)} 个示例")

            elif function_name == "ExtractionTool":
                # 🎯 LLM执行最终的实体提取
                entities = arguments.get("entities", {})
                final_entities = entities
                logger.info(f"✅ 提取到 {sum(len(v) for v in entities.values())} 个实体")

        # 🚀 简化处理：必须有示例才能进行NER
        if not final_entities:
            if retrieved_examples:
                # 使用检索到的示例进行NER
                final_entities = await self._perform_ner_with_examples(original_text, retrieved_examples)
            else:
                # 没有示例就报错，因为按新逻辑必须要有示例
                logger.error("❌ 没有检索到示例，无法进行NER")
                final_entities = {}

        return final_entities

    async def _execute_retrieval(self, retrieval_args: Dict[str, Any], original_text: str) -> List[Dict[str, Any]]:
        """
        🔍 第二环：执行智能订单驱动的多路并行检索
        """
        try:
            if not self.example_retriever or not self.example_retriever.initialized:
                logger.warning("⚠️ 示例检索器未初始化")
                return []

            retrieval_requests = retrieval_args.get("retrieval_requests", [])
            reasoning = retrieval_args.get("reasoning", "")

            logger.info(f"🧠 LLM分析: {reasoning[:100]}...")

            all_examples = []

            # 🔍 为每个检索请求执行多路并行检索
            for request in retrieval_requests:
                examples = await self._multi_path_retrieval(request, original_text)
                all_examples.extend(examples)

            # 🎯 第三环：全局汇总与多样性确保（MMR）
            final_examples = self._select_best_examples(all_examples, max_count=5)

            return final_examples

        except Exception as e:
            logger.error(f"智能订单检索失败: {e}")
            return []

    async def _multi_path_retrieval(self, request: Dict[str, Any], original_text: str) -> List[Dict[str, Any]]:
        """🚀 第二环核心：多路并行检索"""
        try:
            description = request.get("description", "")
            required_entity_types = request.get("required_entity_types", [])

            # 构建搜索查询
            search_query = f"{original_text} {description}".strip()

            # 简化版：直接语义检索
            query_embedding = await self.example_retriever._generate_query_embedding(search_query)
            if not query_embedding:
                return []

            candidates = self.example_retriever.vector_store.similarity_search(
                query_embedding, top_k=30
            )

            # 重排器精排
            if candidates:
                final_examples = await self.example_retriever.reranker_refinement(
                    search_query, candidates, top_k=10
                )
            else:
                final_examples = []

            return final_examples

        except Exception as e:
            logger.error(f"多路检索失败: {e}")
            return []

    def _select_best_examples(self, all_examples: List[Dict[str, Any]], max_count: int = 5) -> List[Dict[str, Any]]:
        """🎯 第三环：MMR全局汇总"""
        if not all_examples:
            return []
        if len(all_examples) <= max_count:
            return all_examples
        # 简化版：直接取前N个
        return all_examples[:max_count]












    async def _perform_ner_with_examples(self, original_text: str, examples: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """🎯 使用检索到的示例进行最终NER"""
        try:
            if not examples:
                # 无示例，直接进行NER
                return await self._perform_direct_ner(original_text)

            # 格式化示例为上下文
            examples_context = self._format_examples_for_context(examples)

            # 构建增强的NER Prompt
            enhanced_prompt = self._build_enhanced_ner_prompt(original_text, examples_context)

            # 执行NER
            tools = [ExtractionTool]
            messages = [{"role": "user", "content": enhanced_prompt}]

            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )

            # 解析结果
            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                tool_call = response.tool_calls[0]
                if tool_call.function and tool_call.function.arguments:
                    try:
                        arguments = json.loads(tool_call.function.arguments)
                        entities = arguments.get("entities", {})
                        return entities
                    except json.JSONDecodeError as e:
                        logger.error(f"解析NER结果失败: {e}")

            logger.warning("增强NER未返回有效结果，尝试直接NER")
            return await self._perform_direct_ner(original_text)

        except Exception as e:
            logger.error(f"示例辅助NER失败: {e}")
            return await self._perform_direct_ner(original_text)

    async def _perform_direct_ner(self, text: str) -> Dict[str, List[str]]:
        """🎯 直接进行NER（无示例辅助）"""
        try:
            entity_types = self._get_current_entity_types()
            entity_types_str = ', '.join(entity_types)
            label_prompt = self._get_current_label_prompt()

            direct_prompt = f"""You are an expert Named Entity Recognition system.

{label_prompt}

Extract named entities from the following text using these entity types: {entity_types_str}

Text to analyze: "{text}"

Extract all named entities and return them using the extract_entities tool."""

            tools = [ExtractionTool]
            messages = [{"role": "user", "content": direct_prompt}]

            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )

            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                tool_call = response.tool_calls[0]
                if tool_call.function and tool_call.function.arguments:
                    try:
                        arguments = json.loads(tool_call.function.arguments)
                        entities = arguments.get("entities", {})
                        return entities
                    except json.JSONDecodeError as e:
                        logger.error(f"解析直接NER结果失败: {e}")

            return {}

        except Exception as e:
            logger.error(f"直接NER失败: {e}")
            return {}

    def _format_examples_for_context(self, examples: List[Dict[str, Any]]) -> str:
        """将检索到的示例格式化为上下文"""
        if not examples:
            return "No examples available."

        formatted_examples = []
        for i, example in enumerate(examples, 1):
            text = example.get('text', '')
            labels = example.get('label', {})

            entities_str = ", ".join(
                f"'{entity}' ({etype})"
                for etype, entities in labels.items()
                for entity in entities
            )

            formatted_examples.append(f"Example {i}:\nText: {text}\nEntities: [{entities_str}]")

        return "\n\n".join(formatted_examples)

    def _build_enhanced_ner_prompt(self, text: str, examples_context: str) -> str:
        """构建包含示例的增强NER Prompt"""
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)
        label_prompt = self._get_current_label_prompt()

        return f"""You are an expert Named Entity Recognition system.

{label_prompt}

Learn from these carefully selected examples:
{examples_context}

Now extract named entities from this text using these entity types: {entity_types_str}

Text to analyze: "{text}"

Extract all named entities and return them using the extract_entities tool."""


# 🚀 全局实例管理
_meta_cognitive_agent = None

def get_meta_cognitive_agent(example_retriever=None):
    """获取元认知智能体实例"""
    global _meta_cognitive_agent
    if _meta_cognitive_agent is None:
        _meta_cognitive_agent = MetaCognitiveAgent(example_retriever)
    return _meta_cognitive_agent
